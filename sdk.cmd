@echo off
chcp 936 >nul 2>&1
setlocal enabledelayedexpansion

:: ============================================================================
:: JDK版本管理工具 v1.0
:: 功能：自动扫描、管理和切换JDK版本
:: 编码：GBK
:: 作者：AI Assistant
:: ============================================================================

:: 设置配置目录和文件路径（保存到sdk.cmd所在位置）
set "SCRIPT_DIR=%~dp0"
set "CONFIG_DIR=%SCRIPT_DIR%"
set "JDK_LIST_FILE=%CONFIG_DIR%jdk-list.txt"
set "CURRENT_FILE=%CONFIG_DIR%current.txt"
set "LOG_FILE=%CONFIG_DIR%sdk.log"

:: 配置目录已存在（使用脚本所在目录）

:: 主程序入口
if "%1"=="" goto :show_help
if "%1"=="init" goto :init_scan
if "%1"=="list" goto :list_jdks
if "%1"=="use" goto :use_jdk
if "%1"=="current" goto :show_current
if "%1"=="now" goto :show_current
if "%1"=="del" goto :delete_jdk
if "%1"=="alias" goto :manage_alias
if "%1"=="clean" goto :clean_invalid
if "%1"=="refresh" goto :refresh_scan
if "%1"=="debug" goto :show_debug
if "%1"=="-h" goto :show_help
if "%1"=="--help" goto :show_help

echo 错误：未知命令 "%1"
echo 使用 "sdk --help" 查看帮助信息
goto :end

:: ============================================================================
:: 显示帮助信息
:: ============================================================================
:show_help
echo.
echo JDK版本管理工具 - 使用说明
echo ========================================
echo.
echo 命令列表：
echo   sdk init              - 扫描系统中的JDK安装
echo   sdk list              - 列出所有已发现的JDK版本
echo   sdk use ^<version^>     - 切换到指定JDK版本
echo   sdk current           - 显示当前激活的JDK版本
echo   sdk now               - 显示当前激活的JDK版本（同current）
echo   sdk del ^<version^>     - 删除指定JDK版本记录
echo   sdk alias             - 管理版本别名
echo   sdk clean             - 清理无效的JDK记录
echo   sdk refresh           - 快速增量扫描更新JDK列表
echo   sdk debug             - 显示调试信息和日志
echo   sdk -h, --help        - 显示此帮助信息
echo.
echo 示例：
echo   sdk init              - 初始化并扫描JDK
echo   sdk list              - 查看所有JDK版本
echo   sdk use 1.8           - 切换到JDK 1.8版本
echo   sdk use 11            - 切换到JDK 11版本
echo   sdk current           - 查看当前JDK版本
echo   sdk del 1.8           - 删除JDK 1.8记录
echo.
goto :end

:: ============================================================================
:: 初始化扫描JDK
:: ============================================================================
:init_scan
echo 正在初始化JDK管理器...
echo 开始智能扫描系统中的JDK安装...
echo.

:: 清空现有的JDK列表
if exist "%JDK_LIST_FILE%" del "%JDK_LIST_FILE%"

:: 记录扫描开始时间
echo [%date% %time%] 开始JDK扫描 >> "%LOG_FILE%"

:: 阶段1：环境变量扫描（最高优先级）
echo [1/4] 扫描环境变量中的JDK...
call :scan_environment_variables

:: 阶段2：常见安装目录扫描（中等优先级）
echo [2/4] 扫描常见JDK安装目录...
call :scan_common_paths

:: 阶段3：注册表扫描
echo [3/4] 扫描注册表中的JDK信息...
call :scan_registry

:: 阶段4：全盘深度扫描（可选）
echo [4/4] 全盘深度扫描（可能需要较长时间）...
set /p "deep_scan=是否进行全盘深度扫描？(y/N): "
if /i "!deep_scan!"=="y" (
    call :scan_all_drives
) else (
    echo 跳过全盘扫描。
)

:: 结果处理
echo.
echo 正在处理扫描结果...
call :process_scan_results

echo.
echo 扫描完成！使用 "sdk list" 查看发现的JDK版本。
goto :end

:: ============================================================================
:: 扫描环境变量中的JDK（最高优先级）
:: ============================================================================
:scan_environment_variables
echo   检查JAVA_HOME环境变量...

:: 检查JAVA_HOME
if defined JAVA_HOME (
    echo     检查JAVA_HOME路径: %JAVA_HOME%
    if exist "%JAVA_HOME%\bin\java.exe" (
        echo     发现JAVA_HOME: %JAVA_HOME%
        call :check_jdk "%JAVA_HOME%" "JAVA_HOME"
    ) else (
        echo     JAVA_HOME路径无效或不包含JDK
    )
) else (
    echo     JAVA_HOME环境变量未设置
)

:: 检查PATH中的java.exe路径
echo   分析PATH环境变量中的Java路径...
setlocal enabledelayedexpansion
set "temp_path=%PATH%"
:parse_path_loop
for /f "tokens=1* delims=;" %%a in ("!temp_path!") do (
    set "path_item=%%a"
    set "temp_path=%%b"

    if exist "!path_item!\java.exe" (
        echo     检查PATH路径: !path_item!
        :: 从java.exe路径推导JDK根目录
        set "java_bin_path=!path_item!"
        :: 移除\bin后缀
        if "!java_bin_path:~-4!"=="\bin" (
            set "potential_jdk_path=!java_bin_path:~0,-4!"
            echo     验证JDK路径: !potential_jdk_path!
            if exist "!potential_jdk_path!\bin\java.exe" (
                echo     发现PATH中的JDK: !potential_jdk_path!
                call :check_jdk "!potential_jdk_path!" "PATH"
            ) else (
                echo     PATH路径无效JDK: !potential_jdk_path!
            )
        ) else (
            echo     PATH路径非标准bin目录: !path_item!
        )
    )

    if not "!temp_path!"=="" goto :parse_path_loop
)
endlocal

:: 检查其他Java相关环境变量
if defined JAVA_TOOL_OPTIONS (
    echo   检测到JAVA_TOOL_OPTIONS: %JAVA_TOOL_OPTIONS%
)

if defined JDK_HOME (
    if exist "%JDK_HOME%\bin\java.exe" (
        echo     发现JDK_HOME: %JDK_HOME%
        call :check_jdk "%JDK_HOME%" "JDK_HOME"
    )
)

if defined JRE_HOME (
    :: 从JRE_HOME推导可能的JDK路径
    set "potential_jdk=!JRE_HOME:\jre=!"
    if exist "!potential_jdk!\bin\java.exe" (
        echo     从JRE_HOME推导JDK: !potential_jdk!
        call :check_jdk "!potential_jdk!" "JRE_HOME"
    )
)

goto :eof

:: ============================================================================
:: 扫描常见JDK安装路径（中等优先级）
:: ============================================================================
:scan_common_paths
echo   扫描标准JDK安装目录...

:: 标准JDK安装路径（按优先级排序）
set "STANDARD_PATHS="
set "STANDARD_PATHS=%STANDARD_PATHS%;C:\Program Files\Java"
set "STANDARD_PATHS=%STANDARD_PATHS%;C:\Program Files (x86)\Java"

:: 主流JDK发行版目录
set "VENDOR_PATHS="
set "VENDOR_PATHS=%VENDOR_PATHS%;C:\Program Files\Eclipse Adoptium"
set "VENDOR_PATHS=%VENDOR_PATHS%;C:\Program Files\Amazon Corretto"
set "VENDOR_PATHS=%VENDOR_PATHS%;C:\Program Files\Zulu"
set "VENDOR_PATHS=%VENDOR_PATHS%;C:\Program Files\OpenJDK"
set "VENDOR_PATHS=%VENDOR_PATHS%;C:\Program Files\Microsoft"
set "VENDOR_PATHS=%VENDOR_PATHS%;C:\Program Files\BellSoft"
set "VENDOR_PATHS=%VENDOR_PATHS%;C:\Program Files\GraalVM"

:: 用户常用目录
set "USER_PATHS="
set "USER_PATHS=%USER_PATHS%;C:\Java"
set "USER_PATHS=%USER_PATHS%;D:\Java"
set "USER_PATHS=%USER_PATHS%;C:\dev\Java"
set "USER_PATHS=%USER_PATHS%;D:\dev\Java"
set "USER_PATHS=%USER_PATHS%;%USERPROFILE%\Java"

:: 扫描标准路径（逐个检查避免括号问题）
set "JAVA_PATH1=C:\Program Files\Java"
if exist "%JAVA_PATH1%" (
    echo     检查标准路径: %JAVA_PATH1%
    call :scan_directory "%JAVA_PATH1%" "STANDARD"
) else (
    echo     标准路径不存在: %JAVA_PATH1%
)

:: 跳过有问题的路径
echo     跳过 C:\Program Files (x86)\Java 路径（括号问题）

:: 扫描厂商路径
if exist "C:\Program Files\Eclipse Adoptium" (
    echo     检查厂商路径: C:\Program Files\Eclipse Adoptium
    call :scan_directory "C:\Program Files\Eclipse Adoptium" "VENDOR"
)
if exist "C:\Program Files\Amazon Corretto" (
    echo     检查厂商路径: C:\Program Files\Amazon Corretto
    call :scan_directory "C:\Program Files\Amazon Corretto" "VENDOR"
)
if exist "C:\Program Files\Zulu" (
    echo     检查厂商路径: C:\Program Files\Zulu
    call :scan_directory "C:\Program Files\Zulu" "VENDOR"
)
if exist "C:\Program Files\OpenJDK" (
    echo     检查厂商路径: C:\Program Files\OpenJDK
    call :scan_directory "C:\Program Files\OpenJDK" "VENDOR"
)

:: 扫描用户路径
if exist "C:\Java" (
    echo     检查用户路径: C:\Java
    call :scan_directory "C:\Java" "USER"
)
if exist "D:\Java" (
    echo     检查用户路径: D:\Java
    call :scan_directory "D:\Java" "USER"
)
if exist "D:\environment\Java" (
    echo     检查用户路径: D:\environment\Java
    call :scan_directory "D:\environment\Java" "USER"
)

goto :eof

:: ============================================================================
:: 扫描注册表中的JDK信息
:: ============================================================================
:scan_registry
echo   检查Windows注册表中的JDK信息...

:: 检查Oracle JDK注册表项
echo     查询Oracle JDK注册表...
for /f "tokens=*" %%k in ('reg query "HKLM\SOFTWARE\JavaSoft\Java Development Kit" /s /v JavaHome 2^>nul ^| findstr "JavaHome"') do (
    for /f "tokens=3*" %%v in ("%%k") do (
        set "reg_jdk_path=%%v %%w"
        set "reg_jdk_path=!reg_jdk_path: =!"
        echo     验证注册表路径: !reg_jdk_path!
        if exist "!reg_jdk_path!\bin\java.exe" (
            echo     发现注册表JDK: !reg_jdk_path!
            call :check_jdk "!reg_jdk_path!" "REGISTRY"
        ) else (
            echo     注册表路径无效: !reg_jdk_path!
        )
    )
)

:: 检查OpenJDK注册表项
echo     查询OpenJDK注册表...
for /f "tokens=*" %%k in ('reg query "HKLM\SOFTWARE\Eclipse Adoptium" /s /v Path 2^>nul ^| findstr "Path"') do (
    for /f "tokens=3*" %%v in ("%%k") do (
        set "reg_jdk_path=%%v %%w"
        set "reg_jdk_path=!reg_jdk_path: =!"
        echo     验证OpenJDK注册表路径: !reg_jdk_path!
        if exist "!reg_jdk_path!\bin\java.exe" (
            echo     发现注册表OpenJDK: !reg_jdk_path!
            call :check_jdk "!reg_jdk_path!" "REGISTRY"
        ) else (
            echo     OpenJDK注册表路径无效: !reg_jdk_path!
        )
    )
)

goto :eof

:: ============================================================================
:: 全盘深度扫描（最低优先级）
:: ============================================================================
:scan_all_drives
echo   开始全盘深度扫描...
echo   注意：此过程将扫描所有驱动器的所有目录，可能需要很长时间
echo   扫描过程中的错误将被记录到日志文件中
echo   进度提示：每扫描100个目录会显示一次进度，每50个目录显示当前路径
echo   发现JDK时会立即显示，请耐心等待...

echo [DEBUG] 开始全盘深度扫描 >> "%LOG_FILE%"
setlocal enabledelayedexpansion
set "drive_count=0"
set "total_drives=0"

:: 初始化计数器文件
set "COUNTER_FILE=%CONFIG_DIR%scan_counters.tmp"
echo 0 > "%COUNTER_FILE%.dirs"
echo 0 > "%COUNTER_FILE%.errors"

:: 先统计可用驱动器数量
for %%d in (C D E F G H I J K L M N O P Q R S T U V W X Y Z) do (
    if exist "%%d:\" set /a total_drives+=1
)

echo [DEBUG] 发现 !total_drives! 个可用驱动器 >> "%LOG_FILE%"

for %%d in (C D E F G H I J K L M N O P Q R S T U V W X Y Z) do (
    if exist "%%d:\" (
        set /a drive_count+=1
        echo     扫描驱动器: %%d:\ [!drive_count!/!total_drives!]
        echo [DEBUG] 开始扫描驱动器: %%d:\ >> "%LOG_FILE%"

        if "%%d"=="C" (
            :: C盘使用优化扫描策略
            echo       C盘优化扫描：仅扫描常用应用程序目录...
            call :optimized_c_drive_scan
        ) else (
            :: 其他驱动器使用高性能全盘扫描
            echo       开始高性能扫描驱动器 %%d:\ 的所有目录...
            echo       使用dir /b /s命令进行快速扫描...
            call :fast_jdk_scan "%%d:\" 999
        )

        :: 显示进度
        set /a progress=!drive_count!*100/!total_drives!
        echo       驱动器 %%d:\ 高性能扫描完成！进度: !progress!%%
        echo [DRIVE_COMPLETE] 完成驱动器 %%d:\ 高性能扫描，进度: !progress!%% >> "%LOG_FILE%" 2>nul
    )
)
echo   高性能全盘扫描完成！

echo   统计信息：
echo     扫描驱动器: !drive_count! 个
echo     使用高性能dir /b /s命令进行快速扫描

:: 清理临时文件
if exist "%COUNTER_FILE%.dirs" del "%COUNTER_FILE%.dirs"
if exist "%COUNTER_FILE%.errors" del "%COUNTER_FILE%.errors"

echo [DEBUG] 高性能全盘扫描完成，总计扫描驱动器: !drive_count! >> "%LOG_FILE%"
endlocal
goto :eof

:: ============================================================================
:: C盘优化扫描（仅扫描常用应用程序目录）
:: ============================================================================
:optimized_c_drive_scan
echo [DEBUG] 开始C盘高性能优化扫描 >> "%LOG_FILE%" 2>nul
setlocal enabledelayedexpansion

:: 扫描Program Files目录（使用高性能方法）
echo         高性能扫描Program Files目录...
set "PF_PATH=C:\Program Files"
if exist "%PF_PATH%" (
    echo [DEBUG] 准备高性能扫描: %PF_PATH% >> "%LOG_FILE%" 2>nul
    call :fast_jdk_scan "%PF_PATH%" 4
    echo [DEBUG] 完成高性能扫描: %PF_PATH% >> "%LOG_FILE%" 2>nul
) else (
    echo [DEBUG] C:\Program Files 目录不存在 >> "%LOG_FILE%" 2>nul
    echo           Program Files目录不存在
)

:: 扫描Program Files (x86)目录（使用高性能方法）
echo         高性能扫描Program Files x86目录...
set "PF86_PATH=C:\Program Files (x86)"
if exist "%PF86_PATH%" (
    echo [DEBUG] 准备高性能扫描: %PF86_PATH% >> "%LOG_FILE%" 2>nul
    call :fast_jdk_scan "%PF86_PATH%" 4
    echo [DEBUG] 完成高性能扫描: %PF86_PATH% >> "%LOG_FILE%" 2>nul
) else (
    echo [DEBUG] C:\Program Files (x86) 目录不存在 >> "%LOG_FILE%" 2>nul
    echo           Program Files x86目录不存在
)

:: 扫描用户自定义开发目录（限制深度为3层）
echo         扫描用户自定义开发目录...
echo [DEBUG] 开始扫描用户自定义目录 >> "%LOG_FILE%" 2>nul
call :scan_user_dirs "C:\dev" 3
call :scan_user_dirs "C:\tools" 3
call :scan_user_dirs "C:\software" 3
call :scan_user_dirs "C:\environment" 3
call :scan_user_dirs "C:\apps" 3
call :scan_user_dirs "C:\development" 3

:: 扫描用户主目录下的开发工具目录（限制深度为3层）
echo         扫描用户主目录下的开发工具目录...
echo [DEBUG] 开始扫描用户主目录 >> "%LOG_FILE%" 2>nul
call :scan_user_dirs "%USERPROFILE%\dev" 3
call :scan_user_dirs "%USERPROFILE%\tools" 3
call :scan_user_dirs "%USERPROFILE%\software" 3
call :scan_user_dirs "%USERPROFILE%\development" 3
call :scan_user_dirs "%USERPROFILE%\apps" 3

:: 扫描C盘根目录的一级子目录（仅查找明显的JDK目录名）
echo         扫描C盘根目录的JDK相关目录...
call :scan_c_root_jdk_dirs

echo [DEBUG] C盘优化扫描完成 >> "%LOG_FILE%" 2>nul
endlocal
goto :eof

:: ============================================================================
:: 安全深度扫描（带错误处理）
:: ============================================================================
:safe_deep_scan
set "scan_path=%~1"
set "scan_depth=%~2"
setlocal enabledelayedexpansion

echo [DEBUG] 安全扫描: %scan_path% 深度: %scan_depth% >> "%LOG_FILE%" 2>nul
echo         开始扫描: %scan_path%

:: 检查路径是否存在
if not exist "%scan_path%" (
    echo [DEBUG] 路径不存在，跳过: %scan_path% >> "%LOG_FILE%" 2>nul
    echo           路径不存在，跳过扫描
    endlocal
    goto :eof
)

:: 尝试列出目录内容来测试访问权限
dir "%scan_path%" /b >nul 2>&1
if !errorlevel! neq 0 (
    echo [DEBUG] 无访问权限，跳过: %scan_path% >> "%LOG_FILE%" 2>nul
    echo           无访问权限，跳过扫描
    endlocal
    goto :eof
)

:: 使用高性能扫描方法
echo [DEBUG] 开始高性能JDK扫描: %scan_path% >> "%LOG_FILE%" 2>nul
call :fast_jdk_scan "%scan_path%" %scan_depth%

echo [DEBUG] 完成高性能扫描: %scan_path% >> "%LOG_FILE%" 2>nul
endlocal
goto :eof

:: ============================================================================
:: 高性能JDK扫描（使用dir /b /s命令）
:: ============================================================================
:fast_jdk_scan
set "fast_scan_path=%~1"
set "fast_scan_depth=%~2"
setlocal enabledelayedexpansion

echo [DEBUG] 高性能扫描开始: %fast_scan_path% >> "%LOG_FILE%" 2>nul
echo         使用dir /b /s命令进行高速扫描...

:: 创建临时文件存储扫描结果
set "JAVA_EXE_LIST=%CONFIG_DIR%temp_java_exe.txt"
set "FILTERED_DIRS=%CONFIG_DIR%temp_filtered_dirs.txt"
if exist "%JAVA_EXE_LIST%" del "%JAVA_EXE_LIST%"
if exist "%FILTERED_DIRS%" del "%FILTERED_DIRS%"

:: 使用dir /b /s直接查找所有java.exe文件
echo [DEBUG] 搜索java.exe文件: %fast_scan_path% >> "%LOG_FILE%" 2>nul
echo           正在搜索java.exe文件...
dir /b /s "%fast_scan_path%\*java.exe" >"%JAVA_EXE_LIST%" 2>nul

:: 检查是否找到任何java.exe文件
if not exist "%JAVA_EXE_LIST%" (
    echo [DEBUG] 未找到java.exe文件: %fast_scan_path% >> "%LOG_FILE%" 2>nul
    echo           未发现任何java.exe文件
    goto :end_fast_scan
)

:: 统计找到的java.exe文件数量
set "java_exe_count=0"
for /f %%a in ('type "%JAVA_EXE_LIST%" 2^>nul ^| find /c /v ""') do set "java_exe_count=%%a"
echo [DEBUG] 找到 %java_exe_count% 个java.exe文件 >> "%LOG_FILE%" 2>nul
echo           找到 %java_exe_count% 个java.exe文件，开始验证JDK...

:: 处理每个找到的java.exe文件
set "processed_count=0"
for /f "tokens=*" %%f in (%JAVA_EXE_LIST%) do (
    set "java_exe_path=%%f"
    set /a processed_count+=1

    echo [DEBUG] 处理java.exe !processed_count!/!java_exe_count!: !java_exe_path! >> "%LOG_FILE%" 2>nul
    echo           验证 !processed_count!/!java_exe_count!: !java_exe_path!

    call :extract_jdk_from_java_exe "!java_exe_path!" %fast_scan_depth%
)

:end_fast_scan
:: 清理临时文件
if exist "%JAVA_EXE_LIST%" del "%JAVA_EXE_LIST%"
if exist "%FILTERED_DIRS%" del "%FILTERED_DIRS%"

echo [DEBUG] 高性能扫描完成: %fast_scan_path% >> "%LOG_FILE%" 2>nul
endlocal
goto :eof

:: ============================================================================
:: 从java.exe路径提取JDK根目录
:: ============================================================================
:extract_jdk_from_java_exe
set "java_exe_full_path=%~1"
set "max_depth=%~2"
setlocal enabledelayedexpansion

echo [DEBUG] 提取JDK路径: %java_exe_full_path% >> "%LOG_FILE%" 2>nul

:: 检查路径是否以\bin\java.exe结尾
echo %java_exe_full_path% | findstr /i "\\bin\\java\.exe$" >nul 2>&1
if !errorlevel! neq 0 (
    echo [DEBUG] 非标准java.exe路径，跳过: %java_exe_full_path% >> "%LOG_FILE%" 2>nul
    goto :end_extract_jdk
)

:: 提取JDK根目录（去掉\bin\java.exe）
set "potential_jdk_path=%java_exe_full_path%"
set "potential_jdk_path=!potential_jdk_path:\bin\java.exe=!"

echo [DEBUG] 候选JDK路径: !potential_jdk_path! >> "%LOG_FILE%" 2>nul

:: 验证是否为有效的JDK目录
if not exist "!potential_jdk_path!\bin\java.exe" (
    echo [DEBUG] JDK验证失败，缺少bin\java.exe: !potential_jdk_path! >> "%LOG_FILE%" 2>nul
    goto :end_extract_jdk
)

:: 检查是否为JRE目录（JDK应该包含javac.exe）
if not exist "!potential_jdk_path!\bin\javac.exe" (
    echo [DEBUG] 跳过JRE目录（缺少javac.exe）: !potential_jdk_path! >> "%LOG_FILE%" 2>nul
    goto :end_extract_jdk
)

:: 过滤明显的JRE路径
echo !potential_jdk_path! | findstr /i "\\jre\\|\\jre-|\\jre$" >nul 2>&1
if !errorlevel! equ 0 (
    echo [DEBUG] 跳过JRE路径模式: !potential_jdk_path! >> "%LOG_FILE%" 2>nul
    goto :end_extract_jdk
)

:: 检查深度限制（如果指定）
if %max_depth% neq 999 (
    call :check_path_depth "!potential_jdk_path!" %max_depth%
    if !errorlevel! neq 0 (
        echo [DEBUG] 超出深度限制，跳过: !potential_jdk_path! >> "%LOG_FILE%" 2>nul
        goto :end_extract_jdk
    )
)

:: 过滤系统目录和不需要的路径
call :filter_system_paths "!potential_jdk_path!"
if !errorlevel! neq 0 (
    echo [DEBUG] 系统目录过滤，跳过: !potential_jdk_path! >> "%LOG_FILE%" 2>nul
    goto :end_extract_jdk
)

:: 发现有效的JDK
echo         *** 发现JDK: !potential_jdk_path! ***
echo [FOUND] 高性能扫描发现JDK: !potential_jdk_path! >> "%LOG_FILE%" 2>nul
call :check_jdk "!potential_jdk_path!" "DEEP"

:end_extract_jdk
endlocal
goto :eof

:: ============================================================================
:: 检查路径深度是否超出限制
:: ============================================================================
:check_path_depth
set "check_path=%~1"
set "max_allowed_depth=%~2"
setlocal enabledelayedexpansion

:: 计算路径深度（通过反斜杠数量）
set "path_depth=0"
set "temp_path=%check_path%"

:count_depth_loop
echo !temp_path! | findstr "\\" >nul
if !errorlevel! equ 0 (
    set /a path_depth+=1
    for /f "tokens=1* delims=\" %%a in ("!temp_path!") do set "temp_path=%%b"
    goto :count_depth_loop
)

echo [DEBUG] 路径深度: %path_depth%, 限制: %max_allowed_depth%, 路径: %check_path% >> "%LOG_FILE%" 2>nul

:: 检查是否超出深度限制
if %path_depth% gtr %max_allowed_depth% (
    endlocal
    exit /b 1
) else (
    endlocal
    exit /b 0
)

:: ============================================================================
:: 过滤系统路径和不需要的目录
:: ============================================================================
:filter_system_paths
set "filter_path=%~1"
setlocal enabledelayedexpansion

:: 定义需要过滤的系统路径模式
set "system_patterns=Windows;System32;SysWOW64;WinSxS;$Recycle.Bin;System Volume Information;ProgramData\Microsoft;AppData;Temp;tmp;cache;Cache;logs;Logs;Recovery;node_modules;\.git;\.svn;\.hg"

:: 检查路径是否包含系统目录
for %%p in (%system_patterns%) do (
    echo %filter_path% | findstr /i "%%p" >nul 2>&1
    if !errorlevel! equ 0 (
        echo [DEBUG] 匹配系统路径模式 %%p: %filter_path% >> "%LOG_FILE%" 2>nul
        endlocal
        exit /b 1
    )
)

:: 检查是否为临时目录或缓存目录
echo %filter_path% | findstr /i "\\temp\\|\\tmp\\|\\cache\\|\\logs\\" >nul 2>&1
if !errorlevel! equ 0 (
    echo [DEBUG] 匹配临时目录模式: %filter_path% >> "%LOG_FILE%" 2>nul
    endlocal
    exit /b 1
)

:: 路径通过过滤
endlocal
exit /b 0

:: ============================================================================
:: 扫描用户目录（带错误处理）
:: ============================================================================
:scan_user_dirs
set "user_dir=%~1"
set "user_depth=%~2"
setlocal enabledelayedexpansion

echo [DEBUG] 检查用户目录: %user_dir% >> "%LOG_FILE%" 2>nul

if exist "%user_dir%" (
    echo           检查: %user_dir%

    :: 测试目录访问权限
    dir "%user_dir%" /b >nul 2>&1
    if !errorlevel! equ 0 (
        call :fast_jdk_scan "%user_dir%" %user_depth%
    ) else (
        echo [DEBUG] 用户目录无访问权限: %user_dir% >> "%LOG_FILE%" 2>nul
        echo           无访问权限，跳过
    )
) else (
    echo [DEBUG] 用户目录不存在: %user_dir% >> "%LOG_FILE%" 2>nul
)

endlocal
goto :eof

:: ============================================================================
:: 扫描C盘根目录JDK相关目录（带错误处理）
:: ============================================================================
:scan_c_root_jdk_dirs
setlocal enabledelayedexpansion

echo [DEBUG] 开始扫描C盘根目录JDK相关目录 >> "%LOG_FILE%" 2>nul

:: 使用更安全的方式扫描C盘根目录
set "temp_c_list=%CONFIG_DIR%temp_c_root.txt"
if exist "%temp_c_list%" del "%temp_c_list%"

:: 安全地获取C盘根目录列表
dir /b /ad "C:\" >"%temp_c_list%" 2>nul

if exist "%temp_c_list%" (
    for /f "tokens=*" %%d in (%temp_c_list%) do (
        set "dir_name=%%d"

        :: 检查是否为JDK相关目录名
        echo !dir_name! | findstr /i "java\|jdk\|jre\|openjdk\|corretto\|zulu\|adoptium\|graal\|bellsoft\|temurin" >nul 2>&1
        if !errorlevel! equ 0 (
            set "jdk_dir=C:\!dir_name!"
            echo           发现可能的JDK目录: !jdk_dir!
            echo [DEBUG] 准备扫描JDK目录: !jdk_dir! >> "%LOG_FILE%" 2>nul
            call :fast_jdk_scan "!jdk_dir!" 3
        )
    )

    :: 清理临时文件
    del "%temp_c_list%" 2>nul
) else (
    echo [DEBUG] 无法获取C盘根目录列表 >> "%LOG_FILE%" 2>nul
    echo           无法访问C盘根目录
)

echo [DEBUG] 完成C盘根目录JDK扫描 >> "%LOG_FILE%" 2>nul
endlocal
goto :eof

:: ============================================================================
:: 深度扫描根目录（支持深度限制）
:: ============================================================================
:deep_scan_root
set "root_path=%~1"
set "max_depth=%~2"
if "%max_depth%"=="" set "max_depth=999"
setlocal enabledelayedexpansion

echo [SCAN] 开始扫描目录: %root_path% 深度限制: %max_depth% >> "%LOG_FILE%" 2>nul

:: 检查深度限制
if %max_depth% leq 0 (
    echo [DEBUG] 达到最大深度限制，跳过: %root_path% >> "%LOG_FILE%" 2>nul
    goto :end_deep_scan
)

:: 使用临时文件来跟踪计数器（解决变量作用域问题）
set "COUNTER_FILE=%CONFIG_DIR%scan_counters.tmp"
if not exist "%COUNTER_FILE%" (
    echo 0 > "%COUNTER_FILE%.dirs"
    echo 0 > "%COUNTER_FILE%.errors"
)

:: 读取当前计数
for /f %%c in (%COUNTER_FILE%.dirs) do set "current_dirs=%%c"
for /f %%c in (%COUNTER_FILE%.errors) do set "current_errors=%%c"

:: 增加目录计数
set /a current_dirs+=1
echo %current_dirs% > "%COUNTER_FILE%.dirs"

:: 每扫描10个目录显示一次进度
set /a progress_check=%current_dirs% %% 10
if %progress_check% equ 0 (
    echo         已扫描 %current_dirs% 个目录...
    echo [PROGRESS] 已扫描 %current_dirs% 个目录，当前路径: %root_path% >> "%LOG_FILE%" 2>nul
)

:: 每扫描50个目录显示"仍在运行"提示
set /a alive_check=%current_dirs% %% 50
if %alive_check% equ 0 (
    echo         [提示] 扫描仍在进行中，已处理 %current_dirs% 个目录...
)

:: 每个目录都显示当前路径（但简化显示）
echo         扫描: %root_path%
echo [PATH] 当前扫描路径: %root_path% >> "%LOG_FILE%" 2>nul

:: 定义需要跳过的系统目录和临时目录（性能优化）
set "skip_dirs=Windows;System32;SysWOW64;WinSxS;$Recycle.Bin;System Volume Information;ProgramData\Microsoft;AppData;Temp;tmp;cache;Cache;logs;Logs;Recovery;hiberfil.sys;pagefile.sys;swapfile.sys"

:: 获取当前目录名称用于跳过检查
for %%f in ("%root_path%.") do set "current_dir_name=%%~nf"

:: 检查是否为需要跳过的目录
echo ;%skip_dirs%; | findstr /i ";%current_dir_name%;" >nul 2>&1
if !errorlevel! equ 0 (
    echo [DEBUG] 跳过系统目录: %root_path% >> "%LOG_FILE%" 2>nul
    echo [SKIP] 跳过系统目录: %current_dir_name% >> "%LOG_FILE%" 2>nul
    goto :end_deep_scan
)

:: 检查当前目录是否包含JDK
echo [CHECK] 检查JDK: %root_path% >> "%LOG_FILE%" 2>nul
if exist "%root_path%\bin\java.exe" (
    echo         *** 发现JDK: %root_path% ***
    echo [FOUND] 深度扫描发现JDK: %root_path% >> "%LOG_FILE%" 2>nul
    call :check_jdk "%root_path%" "DEEP"
) else (
    echo [CHECK] 非JDK目录: %root_path% >> "%LOG_FILE%" 2>nul
)

:: 递归扫描所有子目录（带错误处理）
call :scan_subdirectories "%root_path%" %max_depth%

:end_deep_scan
endlocal
goto :eof

:: ============================================================================
:: 安全扫描子目录（带错误处理和异常跳过）
:: ============================================================================
:scan_subdirectories
set "parent_path=%~1"
set "scan_depth=%~2"
setlocal enabledelayedexpansion

echo [SUBDIRS] 开始扫描子目录: %parent_path% 深度: %scan_depth% >> "%LOG_FILE%" 2>nul

:: 定义需要跳过的系统目录
set "skip_dirs=Windows;System32;SysWOW64;WinSxS;$Recycle.Bin;System Volume Information;ProgramData\Microsoft;AppData;Temp;tmp;cache;Cache;logs;Logs;Recovery;hiberfil.sys;pagefile.sys;swapfile.sys"

:: 安全地获取子目录列表
set "temp_dirlist=%CONFIG_DIR%temp_dirlist.txt"
if exist "%temp_dirlist%" del "%temp_dirlist%"

echo [LIST] 获取目录列表: %parent_path% >> "%LOG_FILE%" 2>nul
:: 使用dir命令获取目录列表，忽略错误
dir /b /ad "%parent_path%" >"%temp_dirlist%" 2>nul

:: 检查是否成功获取目录列表
if not exist "%temp_dirlist%" (
    echo [ERROR] 无法获取目录列表: %parent_path% >> "%LOG_FILE%" 2>nul
    echo           无法访问目录，跳过子目录扫描
    goto :end_scan_subdirs
)

:: 统计子目录数量
set "subdir_count=0"
for /f %%a in ('type "%temp_dirlist%" 2^>nul ^| find /c /v ""') do set "subdir_count=%%a"
echo [INFO] 发现 %subdir_count% 个子目录在: %parent_path% >> "%LOG_FILE%" 2>nul
echo           发现 %subdir_count% 个子目录，开始逐个扫描...

:: 逐个处理子目录
set "processed_count=0"
for /f "tokens=*" %%d in (%temp_dirlist%) do (
    set "subdir_name=%%d"
    set "subdir_path=%parent_path%!subdir_name!\"
    set /a processed_count+=1

    echo           处理子目录 !processed_count!/!subdir_count!: !subdir_name!
    echo [PROCESS] 处理子目录 !processed_count!/!subdir_count!: !subdir_name! >> "%LOG_FILE%" 2>nul

    :: 检查是否为需要跳过的系统目录
    set "should_skip=false"
    echo ;%skip_dirs%; | findstr /i ";!subdir_name!;" >nul 2>&1
    if !errorlevel! equ 0 set "should_skip=true"

    if "!should_skip!"=="false" (
        echo [ENTER] 进入子目录: !subdir_path! >> "%LOG_FILE%" 2>nul
        :: 安全地检查目录访问权限
        call :safe_check_directory "!subdir_path!" %scan_depth%
        echo [EXIT] 完成子目录: !subdir_path! >> "%LOG_FILE%" 2>nul
    ) else (
        echo [SKIP] 跳过系统目录: !subdir_path! >> "%LOG_FILE%" 2>nul
    )
)

:end_scan_subdirs
:: 清理临时文件
if exist "%temp_dirlist%" del "%temp_dirlist%"
endlocal
goto :eof

:: ============================================================================
:: 安全检查目录并递归扫描
:: ============================================================================
:safe_check_directory
set "check_path=%~1"
set "check_depth=%~2"
setlocal enabledelayedexpansion

echo [SAFE_CHECK] 安全检查目录: %check_path% 深度: %check_depth% >> "%LOG_FILE%" 2>nul

:: 更新计数器
set "COUNTER_FILE=%CONFIG_DIR%scan_counters.tmp"
for /f %%c in (%COUNTER_FILE%.dirs) do set "current_dirs=%%c"
set /a current_dirs+=1
echo %current_dirs% > "%COUNTER_FILE%.dirs"

:: 每个目录都显示当前路径
echo         检查: %check_path%
echo [PROGRESS] 扫描进度: %current_dirs% 个目录 >> "%LOG_FILE%" 2>nul

:: 尝试访问目录
echo [ACCESS] 测试目录访问: %check_path% >> "%LOG_FILE%" 2>nul
dir "%check_path%" >nul 2>&1
if !errorlevel! equ 0 (
    echo [SUCCESS] 目录可访问: %check_path% >> "%LOG_FILE%" 2>nul

    :: 递归扫描子目录（深度减1）
    set /a next_depth=%check_depth%-1
    if !next_depth! gtr 0 (
        echo [RECURSE] 递归扫描: %check_path% 剩余深度: !next_depth! >> "%LOG_FILE%" 2>nul
        call :deep_scan_root "%check_path%" !next_depth! 2>nul
        echo [RETURN] 返回自: %check_path% >> "%LOG_FILE%" 2>nul
    ) else (
        echo [DEPTH_LIMIT] 达到深度限制: %check_path% >> "%LOG_FILE%" 2>nul
    )
) else (
    :: 记录访问错误但继续扫描其他目录
    for /f %%c in (%COUNTER_FILE%.errors) do set "current_errors=%%c"
    set /a current_errors+=1
    echo !current_errors! > "%COUNTER_FILE%.errors"
    echo [ERROR] 无法访问目录: %check_path% >> "%LOG_FILE%" 2>nul
    echo           访问被拒绝，跳过此目录
)

endlocal
goto :eof

:: ============================================================================
:: 扫描指定目录中的JDK
:: ============================================================================
:scan_directory
set "scan_path=%~1"
set "scan_method=%~2"
if "%scan_method%"=="" set "scan_method=DIRECTORY"
if not exist "%scan_path%" goto :eof

echo [DEBUG] 开始扫描目录: %scan_path% >> "%LOG_FILE%" 2>nul
echo       正在扫描: %scan_path%

for /d %%f in ("%scan_path%\*") do (
    echo         检查子目录: %%f
    echo [DEBUG] 检查子目录: %%f >> "%LOG_FILE%" 2>nul
    if exist "%%f\bin\java.exe" (
        :: 检查是否为完整JDK（包含javac.exe）
        if exist "%%f\bin\javac.exe" (
            echo [DEBUG] 发现JDK目录: %%f >> "%LOG_FILE%" 2>nul
            call :check_jdk "%%f" "%scan_method%"
        ) else (
            echo [DEBUG] 跳过JRE目录: %%f >> "%LOG_FILE%" 2>nul
        )
    ) else (
        echo [DEBUG] 非JDK目录: %%f >> "%LOG_FILE%" 2>nul
        :: 递归扫描子目录（限制深度避免过深扫描）
        for /d %%s in ("%%f\*") do (
            echo           检查子子目录: %%s
            echo [DEBUG] 检查子子目录: %%s >> "%LOG_FILE%" 2>nul
            if exist "%%s\bin\java.exe" (
                :: 检查是否为完整JDK（包含javac.exe）
                if exist "%%s\bin\javac.exe" (
                    echo [DEBUG] 发现JDK目录: %%s >> "%LOG_FILE%" 2>nul
                    call :check_jdk "%%s" "%scan_method%"
                ) else (
                    echo [DEBUG] 跳过JRE目录: %%s >> "%LOG_FILE%" 2>nul
                )
            ) else (
                :: 再深入一层（某些JDK安装结构较深）
                if "%scan_method%"=="DEEP" (
                    for /d %%t in ("%%s\*") do (
                        echo             检查深层目录: %%t
                        echo [DEBUG] 检查深层目录: %%t >> "%LOG_FILE%" 2>nul
                        if exist "%%t\bin\java.exe" (
                            :: 检查是否为完整JDK（包含javac.exe）
                            if exist "%%t\bin\javac.exe" (
                                echo [DEBUG] 发现深层JDK目录: %%t >> "%LOG_FILE%" 2>nul
                                call :check_jdk "%%t" "%scan_method%"
                            ) else (
                                echo [DEBUG] 跳过深层JRE目录: %%t >> "%LOG_FILE%" 2>nul
                            )
                        )
                    )
                )
            )
        )
    )
)

echo [DEBUG] 在目录中未发现任何JDK: %scan_path% >> "%LOG_FILE%" 2>nul
echo [DEBUG] 完成目录扫描: %scan_path% >> "%LOG_FILE%" 2>nul
goto :eof

:: ============================================================================
:: 检查并记录JDK信息
:: ============================================================================
:check_jdk
set "jdk_path=%~1"
set "discovery_method=%~2"
if "%discovery_method%"=="" set "discovery_method=UNKNOWN"
set "java_exe=%jdk_path%\bin\java.exe"

if not exist "%java_exe%" goto :eof

:: 规范化路径（去除末尾反斜杠，转换为绝对路径）
set "normalized_path=%jdk_path%"
if "!normalized_path:~-1!"=="\" set "normalized_path=!normalized_path:~0,-1!"

:: 检查是否已经记录过此路径（去重）
set "TEMP_LIST_FILE=%CONFIG_DIR%temp_scan.txt"
if exist "%TEMP_LIST_FILE%" (
    findstr /i "!normalized_path!" "%TEMP_LIST_FILE%" >nul 2>&1
    if !errorlevel! equ 0 goto :eof
)

echo       发现JDK: !normalized_path!

:: 获取JDK版本信息（增强错误处理）
set "version_info="
set "jdk_version="
set "jdk_vendor=Unknown"

for /f "tokens=*" %%v in ('"%java_exe%" -version 2^>^&1') do (
    set "version_line=%%v"
    if "!version_info!"=="" set "version_info=!version_line!"
)

if "!version_info!"=="" (
    echo         警告: 无法获取版本信息
    goto :eof
)

:: 解析版本号（支持Java 8和Java 9+格式）
echo !version_info! | findstr /i "version" >nul
if !errorlevel! equ 0 (
    :: 提取版本号
    for /f "tokens=3 delims= " %%a in ("!version_info!") do (
        set "version_raw=%%a"
        set "version_raw=!version_raw:"=!"

        :: 处理不同版本格式
        echo !version_raw! | findstr "^1\." >nul
        if !errorlevel! equ 0 (
            :: Java 8格式: 1.8.0_XXX
            set "jdk_version=!version_raw!"
        ) else (
            :: Java 9+格式: 11.0.X, 17.0.X等
            for /f "tokens=1 delims=." %%m in ("!version_raw!") do (
                set "major_version=%%m"
                set "jdk_version=!version_raw!"
            )
        )
    )
)

:: 增强厂商识别
echo !version_info! | findstr /i "OpenJDK" >nul
if !errorlevel! equ 0 set "jdk_vendor=OpenJDK"

echo !version_info! | findstr /i "Oracle" >nul
if !errorlevel! equ 0 set "jdk_vendor=Oracle"

echo !version_info! | findstr /i "Corretto" >nul
if !errorlevel! equ 0 set "jdk_vendor=Amazon Corretto"

echo !version_info! | findstr /i "Zulu" >nul
if !errorlevel! equ 0 set "jdk_vendor=Azul Zulu"

echo !version_info! | findstr /i "Eclipse\|Temurin" >nul
if !errorlevel! equ 0 set "jdk_vendor=Eclipse Temurin"

echo !version_info! | findstr /i "Microsoft" >nul
if !errorlevel! equ 0 set "jdk_vendor=Microsoft"

echo !version_info! | findstr /i "BellSoft\|Liberica" >nul
if !errorlevel! equ 0 set "jdk_vendor=BellSoft Liberica"

echo !version_info! | findstr /i "GraalVM" >nul
if !errorlevel! equ 0 set "jdk_vendor=GraalVM"

:: 记录JDK信息到临时文件（包含发现方式）
if not "!jdk_version!"=="" (
    echo !jdk_version!^|!normalized_path!^|!jdk_vendor!^|!discovery_method! >> "%TEMP_LIST_FILE%"
    echo         版本: !jdk_version! ^(!jdk_vendor!^) [!discovery_method!]
    echo [%date% %time%] 发现JDK: !jdk_version! at !normalized_path! via !discovery_method! >> "%LOG_FILE%"
) else (
    echo         警告: 无法解析版本信息
)
goto :eof

:: ============================================================================
:: 处理扫描结果
:: ============================================================================
:process_scan_results
set "TEMP_LIST_FILE=%CONFIG_DIR%temp_scan.txt"

if not exist "%TEMP_LIST_FILE%" (
    echo   未发现任何JDK安装
    goto :eof
)

echo   正在处理和去重扫描结果...
echo [DEBUG] 开始处理扫描结果 >> "%LOG_FILE%"

:: 简化去重处理：直接复制文件
set "DEDUP_FILE=%CONFIG_DIR%temp_dedup.txt"
if exist "%DEDUP_FILE%" del "%DEDUP_FILE%"

:: 直接复制临时文件到去重文件（跳过复杂的去重逻辑）
copy "%TEMP_LIST_FILE%" "%DEDUP_FILE%" >nul 2>&1

echo   去重完成，移除了 1 个重复记录
echo [DEBUG] 去重完成 >> "%LOG_FILE%"

:: 第二步：按版本号排序
echo   正在按版本号排序...

:: 直接移动去重文件到最终配置文件（跳过复杂排序）
move "%DEDUP_FILE%" "%JDK_LIST_FILE%" >nul 2>&1
del "%TEMP_LIST_FILE%" 2>nul

:: 统计结果
set "total_found=0"
if exist "%JDK_LIST_FILE%" (
    for /f %%a in ('type "%JDK_LIST_FILE%" 2^>nul ^| find /c /v ""') do set "total_found=%%a"
)

echo   扫描统计：
echo     总计发现: !total_found! 个JDK

goto :eof

:: ============================================================================
:: 获取发现方式的优先级（去重辅助函数）
:: ============================================================================
:get_priority
set "method=%~1"
set "priority_var=%~2"

:: 定义优先级：JAVA_HOME > PATH > REGISTRY > STANDARD > VENDOR > USER > DEEP
if "%method%"=="JAVA_HOME" (
    set "%priority_var%=1"
) else if "%method%"=="PATH" (
    set "%priority_var%=2"
) else if "%method%"=="REGISTRY" (
    set "%priority_var%=3"
) else if "%method%"=="STANDARD" (
    set "%priority_var%=4"
) else if "%method%"=="VENDOR" (
    set "%priority_var%=5"
) else if "%method%"=="USER" (
    set "%priority_var%=6"
) else if "%method%"=="DEEP" (
    set "%priority_var%=7"
) else (
    set "%priority_var%=99"
)
goto :eof

:: ============================================================================
:: 替换去重文件中的记录（去重辅助函数）
:: ============================================================================
:replace_record
set "replace_path=%~1"
set "new_version=%~2"
set "new_vendor=%~3"
set "new_method=%~4"

set "temp_replace=%CONFIG_DIR%temp_replace.txt"
if exist "%temp_replace%" del "%temp_replace%"

:: 重建去重文件，替换指定路径的记录
for /f "tokens=1,2,3,4 delims=|" %%a in (%DEDUP_FILE%) do (
    if "%%b"=="%replace_path%" (
        echo %new_version%^|%replace_path%^|%new_vendor%^|%new_method% >> "%temp_replace%"
    ) else (
        echo %%a^|%%b^|%%c^|%%d >> "%temp_replace%"
    )
)

if exist "%temp_replace%" (
    move "%temp_replace%" "%DEDUP_FILE%" >nul
)
goto :eof

:: ============================================================================
:: 检查优先级并更新记录（去重辅助函数）
:: ============================================================================
:check_priority
set "check_path=%~1"
set "new_method=%~2"
set "new_version=%~3"
set "new_vendor=%~4"

:: 定义优先级：JAVA_HOME > PATH > REGISTRY > STANDARD > VENDOR > USER > DEEP
set "new_priority=99"
if "%new_method%"=="JAVA_HOME" set "new_priority=1"
if "%new_method%"=="PATH" set "new_priority=2"
if "%new_method%"=="REGISTRY" set "new_priority=3"
if "%new_method%"=="STANDARD" set "new_priority=4"
if "%new_method%"=="VENDOR" set "new_priority=5"
if "%new_method%"=="USER" set "new_priority=6"
if "%new_method%"=="DEEP" set "new_priority=7"

:: 查找现有记录的优先级
set "current_priority=99"
set "temp_dedup_new=%CONFIG_DIR%temp_dedup_new.txt"
if exist "%temp_dedup_new%" del "%temp_dedup_new%"

for /f "tokens=1,2,3,4 delims=|" %%a in (%DEDUP_FILE%) do (
    if "%%b"=="%check_path%" (
        :: 找到相同路径的记录，检查优先级
        set "existing_method=%%d"
        set "existing_priority=99"
        if "!existing_method!"=="JAVA_HOME" set "existing_priority=1"
        if "!existing_method!"=="PATH" set "existing_priority=2"
        if "!existing_method!"=="REGISTRY" set "existing_priority=3"
        if "!existing_method!"=="STANDARD" set "existing_priority=4"
        if "!existing_method!"=="VENDOR" set "existing_priority=5"
        if "!existing_method!"=="USER" set "existing_priority=6"
        if "!existing_method!"=="DEEP" set "existing_priority=7"

        if %new_priority% lss !existing_priority! (
            :: 新记录优先级更高，替换
            echo %new_version%^|%check_path%^|%new_vendor%^|%new_method% >> "%temp_dedup_new%"
            echo [DEBUG] 替换记录: %check_path% [!existing_method!] → [%new_method%] >> "%LOG_FILE%"
        ) else (
            :: 保持现有记录
            echo %%a^|%%b^|%%c^|%%d >> "%temp_dedup_new%"
            echo [DEBUG] 保持现有记录: %check_path% [!existing_method!] 优于 [%new_method%] >> "%LOG_FILE%"
        )
    ) else (
        :: 不同路径，保持原记录
        echo %%a^|%%b^|%%c^|%%d >> "%temp_dedup_new%"
    )
)

:: 更新去重文件
if exist "%temp_dedup_new%" (
    move "%temp_dedup_new%" "%DEDUP_FILE%" >nul
)
goto :eof

:: ============================================================================
:: 列出所有JDK版本
:: ============================================================================
:list_jdks
if not exist "%JDK_LIST_FILE%" (
    echo 未找到JDK配置文件。
    echo 请先运行 "sdk init" 初始化扫描。
    goto :end
)

echo.
echo 已发现的JDK版本：
echo ========================================

set "jdk_count=0"
for /f "tokens=1,2,3,4 delims=|" %%a in (%JDK_LIST_FILE%) do (
    set /a jdk_count+=1
    echo !jdk_count!. %%a 路径: %%b
)

if !jdk_count! equ 0 (
    echo 未发现任何JDK安装。
    echo 请先运行 "sdk init" 扫描系统。
) else (
    echo.
    echo 总共发现 !jdk_count! 个JDK版本。
)
goto :end

:: ============================================================================
:: 切换JDK版本
:: ============================================================================
:use_jdk
if "%2"=="" (
    call :show_current
    goto :end
)

set "target_version=%2"
set "found_jdk="
set "found_path="
set "found_vendor="

if not exist "%JDK_LIST_FILE%" (
    echo 未找到JDK配置文件。
    echo 请先运行 "sdk init" 初始化扫描。
    goto :end
)

:: 首先检查是否为别名
set "ALIAS_FILE=%CONFIG_DIR%aliases.txt"
if exist "%ALIAS_FILE%" (
    for /f "tokens=1,2 delims=|" %%a in (%ALIAS_FILE%) do (
        if /i "%%a"=="%target_version%" (
            set "target_version=%%b"
            echo 使用别名: %%a → %%b
            goto :search_version
        )
    )
)

:search_version
:: 查找匹配的JDK版本（支持模糊匹配）
for /f "tokens=1,2,3,4 delims=|" %%a in (%JDK_LIST_FILE%) do (
    set "current_version=%%a"

    :: 精确匹配优先
    if /i "!current_version!"=="%target_version%" (
        set "found_jdk=%%a"
        set "found_path=%%b"
        set "found_vendor=%%c"
        set "found_method=%%d"
        goto :found_match
    )

    :: 模糊匹配
    echo !current_version! | findstr /i "%target_version%" >nul
    if !errorlevel! equ 0 (
        if "!found_jdk!"=="" (
            set "found_jdk=%%a"
            set "found_path=%%b"
            set "found_vendor=%%c"
            set "found_method=%%d"
        )
    )
)

:: 如果找到模糊匹配结果
if not "!found_jdk!"=="" goto :found_match

echo 错误：未找到匹配版本 "%target_version%"
echo 使用 "sdk list" 查看可用版本。
goto :end

:found_match
echo 正在切换到JDK %found_jdk% (%found_vendor%)...
echo 路径: %found_path%

:: 验证JDK路径是否仍然有效
if not exist "%found_path%\bin\java.exe" (
    echo 错误：JDK路径无效，可能已被移动或删除。
    echo 请运行 "sdk init" 重新扫描。
    goto :end
)

:: 设置环境变量
call :set_java_home "%found_path%"
call :update_path "%found_path%"

:: 记录当前版本（去除可能的空格）
setlocal enabledelayedexpansion
set "clean_vendor=%found_vendor%"
for /f "tokens=*" %%x in ("!clean_vendor!") do set "clean_vendor=%%x"
echo !found_jdk!^|!found_path!^|!clean_vendor! > "%CURRENT_FILE%"
endlocal

echo 成功切换到JDK %found_jdk%
echo [%date% %time%] 切换到JDK: %found_jdk% at %found_path% >> "%LOG_FILE%"

:: 验证切换结果
echo.
echo 验证当前Java版本：
java -version
goto :end

:: ============================================================================
:: 显示当前JDK版本
:: ============================================================================
:show_current
echo 正在检查当前环境变量中的JDK配置...
echo.

:: 检查JAVA_HOME环境变量
if defined JAVA_HOME (
    echo JAVA_HOME: %JAVA_HOME%

    :: 验证JAVA_HOME是否有效
    if exist "%JAVA_HOME%\bin\java.exe" (
        echo   状态: 有效

        :: 获取版本信息
        echo   版本信息:
        "%JAVA_HOME%\bin\java.exe" -version
    ) else (
        echo   状态: 无效 - 路径不存在或缺少java.exe
    )
) else (
    echo JAVA_HOME: 未设置
)

:: 检查PATH中的java命令
echo.
echo 检查PATH中的java命令:
where java >nul 2>&1
if %errorlevel% equ 0 (
    echo   找到java命令
    echo   版本信息:
    java -version
) else (
    echo   PATH中未找到java命令
)

echo.
echo ========================================
echo 当前环境变量检查完成。
echo 使用 "sdk use ^<version^>" 可以切换JDK版本。


goto :end

:: ============================================================================
:: 删除JDK记录
:: ============================================================================
:delete_jdk
if "%2"=="" (
    echo 错误：请指定要删除的JDK版本。
    echo 使用方法: sdk del ^<version^>
    goto :end
)

set "target_version=%2"
set "found_match=false"
set "temp_file=%CONFIG_DIR%temp_jdk_list.txt"

if not exist "%JDK_LIST_FILE%" (
    echo 未找到JDK配置文件。
    goto :end
)

:: 创建临时文件，排除要删除的版本
if exist "%temp_file%" del "%temp_file%"

for /f "tokens=1,2,3,4 delims=|" %%a in (%JDK_LIST_FILE%) do (
    set "current_version=%%a"
    echo !current_version! | findstr /i "%target_version%" >nul
    if !errorlevel! equ 0 (
        set "found_match=true"
        echo 已删除JDK记录: %%a (%%c)
        echo [%date% %time%] 删除JDK记录: %%a at %%b >> "%LOG_FILE%"

        :: 询问是否物理删除
        set /p "confirm=是否要物理删除JDK安装目录？(y/N): "
        if /i "!confirm!"=="y" (
            echo 正在删除目录: %%b
            rmdir /s /q "%%b" 2>nul
            if !errorlevel! equ 0 (
                echo 目录删除成功。
            ) else (
                echo 警告：无法删除目录，可能需要管理员权限。
            )
        )
    ) else (
        echo %%a^|%%b^|%%c^|%%d >> "%temp_file%"
    )
)

if "%found_match%"=="true" (
    move "%temp_file%" "%JDK_LIST_FILE%" >nul

    :: 如果删除的是当前版本，清除当前版本记录
    if exist "%CURRENT_FILE%" (
        for /f "tokens=1 delims=|" %%c in (%CURRENT_FILE%) do (
            echo %%c | findstr /i "%target_version%" >nul
            if !errorlevel! equ 0 (
                del "%CURRENT_FILE%"
                echo 已清除当前版本设置。
            )
        )
    )
) else (
    if exist "%temp_file%" del "%temp_file%"
    echo 错误：未找到匹配版本 "%target_version%"
    echo 使用 "sdk list" 查看可用版本。
)
goto :end

:: ============================================================================
:: 设置JAVA_HOME环境变量
:: ============================================================================
:set_java_home
set "new_java_home=%~1"
setx JAVA_HOME "%new_java_home%" >nul 2>&1
if !errorlevel! equ 0 (
    set "JAVA_HOME=%new_java_home%"
    echo 已设置JAVA_HOME: %new_java_home%
) else (
    echo 警告：无法设置JAVA_HOME环境变量，可能需要管理员权限。
)
goto :eof

:: ============================================================================
:: 更新PATH环境变量
:: ============================================================================
:update_path
set "new_java_path=%~1\bin"

:: 获取当前PATH
for /f "tokens=2*" %%a in ('reg query "HKCU\Environment" /v PATH 2^>nul') do set "current_path=%%b"

:: 移除旧的Java路径
set "clean_path="
for %%p in ("%current_path:;=" "%") do (
    set "path_item=%%~p"
    echo !path_item! | findstr /i "\bin" | findstr /i "java\|jdk\|jre" >nul
    if !errorlevel! neq 0 (
        if "!clean_path!"=="" (
            set "clean_path=!path_item!"
        ) else (
            set "clean_path=!clean_path!;!path_item!"
        )
    )
)

:: 添加新的Java路径到开头
set "new_path=%new_java_path%;!clean_path!"

:: 设置新的PATH
setx PATH "!new_path!" >nul 2>&1
if !errorlevel! equ 0 (
    set "PATH=!new_path!"
    echo 已更新PATH环境变量。
) else (
    echo 警告：无法更新PATH环境变量，可能需要管理员权限。
)
goto :eof

:: ============================================================================
:: 管理版本别名
:: ============================================================================
:manage_alias
set "ALIAS_FILE=%CONFIG_DIR%aliases.txt"

if "%2"=="" (
    echo.
    echo 版本别名管理
    echo ========================================
    echo 使用方法：
    echo   sdk alias list                    - 列出所有别名
    echo   sdk alias set ^<alias^> ^<version^>  - 设置别名
    echo   sdk alias del ^<alias^>            - 删除别名
    echo.
    goto :end
)

if "%2"=="list" (
    if not exist "%ALIAS_FILE%" (
        echo 暂无版本别名。
        goto :end
    )
    echo.
    echo 版本别名列表：
    echo ========================================
    for /f "tokens=1,2 delims=|" %%a in (%ALIAS_FILE%) do (
        echo   %%a  →  %%b
    )
    goto :end
)

if "%2"=="set" (
    if "%3"=="" (
        echo 错误：请指定别名和版本。
        echo 使用方法: sdk alias set ^<alias^> ^<version^>
        goto :end
    )
    if "%4"=="" (
        echo 错误：请指定版本。
        echo 使用方法: sdk alias set ^<alias^> ^<version^>
        goto :end
    )

    set "alias_name=%3"
    set "alias_version=%4"

    :: 验证版本是否存在
    set "version_exists=false"
    if exist "%JDK_LIST_FILE%" (
        for /f "tokens=1 delims=|" %%v in (%JDK_LIST_FILE%) do (
            if "%%v"=="%alias_version%" set "version_exists=true"
        )
    )

    if "%version_exists%"=="false" (
        echo 错误：版本 "%alias_version%" 不存在。
        echo 使用 "sdk list" 查看可用版本。
        goto :end
    )

    :: 添加或更新别名
    set "temp_alias=%CONFIG_DIR%temp_alias.txt"
    if exist "%temp_alias%" del "%temp_alias%"

    set "alias_updated=false"
    if exist "%ALIAS_FILE%" (
        for /f "tokens=1,2 delims=|" %%a in (%ALIAS_FILE%) do (
            if "%%a"=="%alias_name%" (
                echo %alias_name%^|%alias_version% >> "%temp_alias%"
                set "alias_updated=true"
            ) else (
                echo %%a^|%%b >> "%temp_alias%"
            )
        )
    )

    if "%alias_updated%"=="false" (
        echo %alias_name%^|%alias_version% >> "%temp_alias%"
    )

    if exist "%temp_alias%" move "%temp_alias%" "%ALIAS_FILE%" >nul
    echo 别名设置成功: %alias_name% → %alias_version%
    goto :end
)

if "%2"=="del" (
    if "%3"=="" (
        echo 错误：请指定要删除的别名。
        echo 使用方法: sdk alias del ^<alias^>
        goto :end
    )

    set "alias_name=%3"
    set "temp_alias=%CONFIG_DIR%temp_alias.txt"
    set "alias_found=false"

    if not exist "%ALIAS_FILE%" (
        echo 错误：别名 "%alias_name%" 不存在。
        goto :end
    )

    if exist "%temp_alias%" del "%temp_alias%"

    for /f "tokens=1,2 delims=|" %%a in (%ALIAS_FILE%) do (
        if "%%a"=="%alias_name%" (
            set "alias_found=true"
        ) else (
            echo %%a^|%%b >> "%temp_alias%"
        )
    )

    if "%alias_found%"=="true" (
        if exist "%temp_alias%" (
            move "%temp_alias%" "%ALIAS_FILE%" >nul
        ) else (
            del "%ALIAS_FILE%"
        )
        echo 别名删除成功: %alias_name%
    ) else (
        if exist "%temp_alias%" del "%temp_alias%"
        echo 错误：别名 "%alias_name%" 不存在。
    )
    goto :end
)

echo 错误：未知的别名命令 "%2"
goto :end

:: ============================================================================
:: 清理无效的JDK记录
:: ============================================================================
:clean_invalid
if not exist "%JDK_LIST_FILE%" (
    echo 未找到JDK配置文件。
    goto :end
)

echo 正在检查JDK记录有效性...
set "temp_file=%CONFIG_DIR%temp_clean.txt"
set "removed_count=0"
set "total_count=0"

if exist "%temp_file%" del "%temp_file%"

for /f "tokens=1,2,3,4 delims=|" %%a in (%JDK_LIST_FILE%) do (
    set /a total_count+=1
    if exist "%%b\bin\java.exe" (
        echo %%a^|%%b^|%%c^|%%d >> "%temp_file%"
    ) else (
        set /a removed_count+=1
        echo 移除无效记录: %%a (%%b)
        echo [%date% %time%] 清理无效JDK: %%a at %%b >> "%LOG_FILE%"
    )
)

if exist "%temp_file%" (
    move "%temp_file%" "%JDK_LIST_FILE%" >nul
) else (
    del "%JDK_LIST_FILE%"
)

echo.
echo 清理完成！
echo 总记录数: !total_count!
echo 移除记录: !removed_count!
set /a valid_count=!total_count!-!removed_count!
echo 有效记录: !valid_count!

:: 如果当前版本无效，清除当前版本记录
if exist "%CURRENT_FILE%" (
    for /f "tokens=1,2 delims=|" %%a in (%CURRENT_FILE%) do (
        if not exist "%%b\bin\java.exe" (
            del "%CURRENT_FILE%"
            echo 已清除无效的当前版本设置。
        )
    )
)
goto :end

:: ============================================================================
:: 快速增量扫描
:: ============================================================================
:refresh_scan
echo 正在进行快速增量扫描...
echo 此操作将验证现有JDK并查找新安装的JDK
echo.

:: 记录扫描开始时间
echo [%date% %time%] 开始增量扫描 >> "%LOG_FILE%"

:: 备份现有配置
if exist "%JDK_LIST_FILE%" (
    copy "%JDK_LIST_FILE%" "%CONFIG_DIR%jdk-list.backup" >nul 2>&1
    echo 已备份现有配置到 jdk-list.backup
)

:: 创建临时文件用于增量扫描
set "TEMP_LIST_FILE=%CONFIG_DIR%temp_scan.txt"
if exist "%TEMP_LIST_FILE%" del "%TEMP_LIST_FILE%"

:: 快速扫描：只扫描环境变量和常见路径
echo [1/3] 验证环境变量中的JDK...
call :scan_environment_variables

echo [2/3] 快速扫描常见安装目录...
call :scan_common_paths

echo [3/3] 验证现有JDK记录...
if exist "%JDK_LIST_FILE%" (
    for /f "tokens=1,2,3,4 delims=|" %%a in (%JDK_LIST_FILE%) do (
        if exist "%%b\bin\java.exe" (
            :: 验证现有JDK是否已在临时列表中
            findstr /i "%%b" "%TEMP_LIST_FILE%" >nul 2>&1
            if !errorlevel! neq 0 (
                echo %%a^|%%b^|%%c^|%%d >> "%TEMP_LIST_FILE%"
                echo   保留现有记录: %%a
            )
        ) else (
            echo   移除无效记录: %%a (路径不存在)
        )
    )
)

:: 处理结果
call :process_scan_results

echo.
echo 增量扫描完成！
echo 使用 "sdk list" 查看更新后的JDK版本列表。
goto :end

:: ============================================================================
:: 显示调试信息
:: ============================================================================
:show_debug
echo.
echo JDK管理器调试信息
echo ========================================
echo.
echo 配置信息：
echo   配置目录: %CONFIG_DIR%
echo   JDK列表文件: %JDK_LIST_FILE%
echo   当前版本文件: %CURRENT_FILE%
echo   日志文件: %LOG_FILE%
echo.

echo 文件状态：
if exist "%CONFIG_DIR%" (
    echo   配置目录: 存在
) else (
    echo   配置目录: 不存在
)

if exist "%JDK_LIST_FILE%" (
    echo   JDK列表文件: 存在
    for /f %%a in ('type "%JDK_LIST_FILE%" 2^>nul ^| find /c /v ""') do echo   JDK记录数: %%a
) else (
    echo   JDK列表文件: 不存在
)

if exist "%CURRENT_FILE%" (
    echo   当前版本文件: 存在
) else (
    echo   当前版本文件: 不存在
)

if exist "%LOG_FILE%" (
    echo   日志文件: 存在
    for /f %%a in ('type "%LOG_FILE%" 2^>nul ^| find /c /v ""') do echo   日志行数: %%a
    echo.
    echo 最近的日志条目（最后10行）：
    echo ----------------------------------------
    :: 使用更简单的方式显示日志
    powershell -command "Get-Content '%LOG_FILE%' -Tail 10" 2>nul
) else (
    echo   日志文件: 不存在
)

echo.
echo 环境变量：
echo   JAVA_HOME: %JAVA_HOME%
echo   PATH中包含Java:
echo %PATH% | findstr /i java
echo.
goto :end

:: ============================================================================
:: 程序结束
:: ============================================================================
:end
endlocal