# JDK管理工具 - 快速开始指南

## 🚀 5分钟快速上手

### 第一步：安装
```cmd
# 方法1：自动安装（推荐）
install.cmd

# 方法2：手动安装
# 将sdk.cmd复制到C:\Tools\目录，并添加到PATH环境变量
```

### 第二步：初始化
```cmd
# 扫描系统中的所有JDK（首次使用必须执行）
sdk init
```

### 第三步：查看JDK版本
```cmd
# 列出所有发现的JDK版本
sdk list
```

### 第四步：切换JDK版本
```cmd
# 切换到JDK 11
sdk use 11

# 切换到JDK 1.8
sdk use 1.8

# 切换到特定版本
sdk use 1.8.0_301
```

### 第五步：验证切换结果
```cmd
# 查看当前JDK版本
sdk current

# 验证Java命令
java -version
```

## 📋 常用命令速查

| 命令 | 功能 | 示例 |
|------|------|------|
| `sdk init` | 扫描系统JDK | `sdk init` |
| `sdk list` | 列出所有JDK | `sdk list` |
| `sdk use <版本>` | 切换JDK版本 | `sdk use 11` |
| `sdk current` | 显示当前版本 | `sdk current` |
| `sdk del <版本>` | 删除JDK记录 | `sdk del 1.8` |
| `sdk alias` | 管理版本别名 | `sdk alias set lts 11` |
| `sdk clean` | 清理无效记录 | `sdk clean` |
| `sdk --help` | 显示帮助 | `sdk --help` |

## 🎯 实用技巧

### 1. 版本别名管理
```cmd
# 为长版本号设置简短别名
sdk alias set lts 11.0.12
sdk alias set legacy 1.8.0_301

# 使用别名切换
sdk use lts
sdk use legacy

# 查看所有别名
sdk alias list

# 删除别名
sdk alias del lts
```

### 2. 模糊版本匹配
```cmd
# 以下命令都可以匹配到JDK 1.8.0_301
sdk use 1.8
sdk use 1.8.0
sdk use 301
sdk use oracle  # 按厂商匹配
```

### 3. 批量操作
```cmd
# 清理无效的JDK记录
sdk clean

# 重新扫描（更新JDK列表）
sdk init
```

### 4. 快速切换工作流
```cmd
# 开发Java 8项目
sdk use 1.8
java -version
mvn clean compile

# 切换到Java 11项目
sdk use 11
java -version
gradle build
```

## 🔧 故障排除

### 问题1：扫描不到JDK
**解决方案：**
- 确认JDK安装目录包含`bin\java.exe`
- 检查是否安装在非标准路径
- 手动运行`sdk init`重新扫描

### 问题2：切换版本后java命令仍是旧版本
**解决方案：**
- 重新打开命令提示符
- 检查PATH中是否有其他Java路径
- 以管理员身份运行脚本

### 问题3：环境变量设置失败
**解决方案：**
- 以管理员身份运行命令提示符
- 手动设置JAVA_HOME和PATH环境变量
- 检查系统权限设置

### 问题4：中文显示乱码
**解决方案：**
- 确保命令提示符使用GBK编码
- 运行`chcp 936`设置代码页
- 使用支持中文的终端

## 📁 配置文件说明

工具在`%USERPROFILE%\.jdk-manager\`目录下创建以下文件：

- **jdk-list.txt**：JDK版本列表
- **current.txt**：当前激活版本
- **aliases.txt**：版本别名配置
- **sdk.log**：操作日志

## 🎨 高级用法

### 1. 项目级JDK配置
在项目根目录创建`.jdk-version`文件：
```
11.0.12
```

然后使用脚本自动切换：
```cmd
@echo off
if exist .jdk-version (
    for /f %%v in (.jdk-version) do sdk use %%v
)
```

### 2. 批处理脚本集成
```cmd
@echo off
echo 设置开发环境...
sdk use 11
echo 当前JDK版本：
sdk current
echo 开始构建项目...
mvn clean package
```

### 3. 多环境管理
```cmd
# 设置开发环境
sdk alias set dev 11.0.12
sdk alias set test 1.8.0_301
sdk alias set prod 17.0.2

# 快速切换
sdk use dev    # 开发环境
sdk use test   # 测试环境
sdk use prod   # 生产环境
```

## 📞 获取帮助

- 查看完整帮助：`sdk --help`
- 查看日志文件：`%USERPROFILE%\.jdk-manager\sdk.log`
- 测试工具功能：运行`test_sdk.cmd`

## 🔄 更新和维护

### 定期维护
```cmd
# 清理无效记录
sdk clean

# 重新扫描新安装的JDK
sdk init
```

### 备份配置
```cmd
# 备份配置目录
xcopy "%USERPROFILE%\.jdk-manager" "backup\.jdk-manager" /E /I
```

---

**提示：** 首次使用建议运行`test_sdk.cmd`测试所有功能是否正常工作。
